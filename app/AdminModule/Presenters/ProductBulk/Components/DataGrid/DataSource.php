<?php declare(strict_types=1);

namespace App\AdminModule\Presenters\ProductBulk\Components\DataGrid;

use App\Model\ElasticSearch\Product\Repository;
use App\Model\Orm\EsIndex\EsIndex;
use Elastica\Query\BoolQuery;
use Elastica\ResultSet;
use Ublaboo\DataGrid\DataSource\IDataSource;
use Ublaboo\DataGrid\Utils\Sorting;

class DataSource implements IDataSource
{

	private ?ResultSet $results = null;

	public function __construct(
		private readonly BoolQuery $boolQuery,
		private readonly EsIndex $esIndex,
		private readonly Repository $esProductRepository,
	)
	{
	}

	private int $limit = 50;

	private int $offset = 0;

	private Sorting $sorting; // @phpstan-ignore-line

	private function getResults(): ResultSet
	{
		if ($this->results === null) {
			$this->results = $this->esProductRepository->findByQuery($this->esIndex, $this->boolQuery, $this->limit, $this->offset);
		}

		return $this->results;
	}

	public function getCount(): int
	{
		return $this->getResults()->getTotalHits();
	}

	public function getData(): iterable
	{
		return $this->getResults()->getResults();
	}


	public function filter(array $filters): void
	{
		// TODO: Implement filter() method.
	}

	public function filterOne(array $condition): IDataSource
	{
		// TODO: Implement filterOne() method.
		return $this;
	}

	public function limit(int $offset, int $limit): IDataSource
	{
		$this->limit = $limit;
		$this->offset = $offset;

		return $this;
	}

	public function sort(Sorting $sorting): IDataSource
	{
		$this->sorting = $sorting;

		return $this;
	}

}
