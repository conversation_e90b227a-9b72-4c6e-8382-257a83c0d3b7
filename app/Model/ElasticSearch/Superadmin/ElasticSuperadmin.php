<?php declare(strict_types = 1);

namespace App\Model\ElasticSearch\Superadmin;

use App\Model\ElasticSearch\Entity;
use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\Product\Product;
use LogicException;

/**
 * @param Convertor[] $convertors
 */
class ElasticSuperadmin implements Entity
{

	public const string TYPE_PRODUCT_VARIANT = 'product_variant';
	public function __construct(
		private object $object,
		private readonly array $convertors = [],
	)
	{
	}

	public function getId(): string
	{
		$class = get_class($this->object);

		if (isset($this->object->id)) {
			return match ($class) {
				Product::class => self::TYPE_PRODUCT_VARIANT . '-' . $this->object->id,
				default => throw new LogicException(sprintf("Missing definition for '%s' class", $class))
			};
		} else {
			throw new LogicException('Missing primary key for entity');
		}
	}

	public function getData(Mutation $mutation): array
	{
		$convertedData = [];
		foreach ($this->convertors as $convertor) {
			$convertedData[] = $convertor->convert($this->object);
		}

		return array_merge(...$convertedData);
	}

}
